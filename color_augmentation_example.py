#!/usr/bin/env python3
# -*-coding:utf-8-*-
"""
颜色增强使用示例
================

展示如何在实际项目中使用SynchronizedColorAugmentation类
"""

import cv2
import numpy as np
from pathlib import Path
from typing import List
from color_augmentation import SynchronizedColorAugmentation


def load_images_from_paths(image_paths: List[str]) -> List[np.ndarray]:
    """
    从文件路径加载图片
    
    Args:
        image_paths (List[str]): 图片文件路径列表
        
    Returns:
        List[np.ndarray]: 加载的图片列表
    """
    images = []
    for path in image_paths:
        if not Path(path).exists():
            raise FileNotFoundError(f"图片文件不存在: {path}")
        
        img = cv2.imread(path)
        if img is None:
            raise ValueError(f"无法读取图片: {path}")
        
        images.append(img)
        print(f"已加载图片: {path}, 形状: {img.shape}")
    
    return images


def save_augmented_images(images: List[np.ndarray], 
                         output_dir: str, 
                         prefix: str = "augmented") -> None:
    """
    保存增强后的图片
    
    Args:
        images (List[np.ndarray]): 图片列表
        output_dir (str): 输出目录
        prefix (str): 文件名前缀
    """
    output_path = Path(output_dir)
    output_path.mkdir(exist_ok=True, parents=True)
    
    for i, img in enumerate(images):
        filename = f"{prefix}_{i+1}.jpg"
        filepath = output_path / filename
        cv2.imwrite(str(filepath), img)
        print(f"已保存: {filepath}")


def example_with_real_images():
    """使用真实图片的示例"""
    # 示例图片路径（请根据实际情况修改）
    image_paths = [
        "path/to/image1.jpg",
        "path/to/image2.jpg", 
        "path/to/image3.jpg"
    ]
    
    try:
        # 加载图片
        images = load_images_from_paths(image_paths)
        
        # 创建颜色增强器
        augmenter = SynchronizedColorAugmentation(
            brightness=0.4,    # 亮度变化范围 ±40%
            contrast=0.3,      # 对比度变化范围 ±30%
            saturation=0.3,    # 饱和度变化范围 ±30%
            hue=0.1,          # 色调变化范围 ±0.1
            random_seed=None   # 不设置随机种子，每次结果不同
        )
        
        # 执行颜色增强
        augmented_images = augmenter.augment_images(images)
        
        # 保存结果
        save_augmented_images(images, "output/original", "original")
        save_augmented_images(augmented_images, "output/augmented", "augmented")
        
        print("颜色增强完成！")
        
    except (FileNotFoundError, ValueError) as e:
        print(f"错误: {e}")
        print("将运行模拟数据示例...")
        example_with_synthetic_images()


def example_with_synthetic_images():
    """使用合成图片的示例"""
    print("创建合成测试图片...")
    
    # 创建3张不同的测试图片
    images = []
    
    # 图片1: 渐变彩色图
    img1 = np.zeros((300, 300, 3), dtype=np.uint8)
    for i in range(300):
        img1[i, :, 0] = int(255 * i / 300)  # B通道渐变
        img1[:, i, 1] = int(255 * i / 300)  # G通道渐变
        img1[i, i, 2] = 255                 # R通道对角线
    images.append(img1)
    
    # 图片2: 几何图形
    img2 = np.ones((300, 300, 3), dtype=np.uint8) * 128
    cv2.circle(img2, (150, 150), 80, (255, 100, 50), -1)
    cv2.rectangle(img2, (50, 50), (250, 100), (50, 255, 100), -1)
    cv2.ellipse(img2, (150, 200), (100, 50), 0, 0, 360, (100, 50, 255), -1)
    images.append(img2)
    
    # 图片3: 纹理图案
    img3 = np.zeros((300, 300, 3), dtype=np.uint8)
    for i in range(0, 300, 20):
        for j in range(0, 300, 20):
            color = ((i + j) % 255, (i * 2) % 255, (j * 2) % 255)
            cv2.rectangle(img3, (j, i), (j+20, i+20), color, -1)
    images.append(img3)
    
    # 创建颜色增强器
    augmenter = SynchronizedColorAugmentation(
        brightness=0.3,
        contrast=0.4,
        saturation=0.5,
        hue=0.15,
        random_seed=42  # 设置随机种子以便复现结果
    )
    
    # 执行颜色增强
    print("执行颜色增强...")
    augmented_images = augmenter.augment_images(images)
    
    # 保存结果
    save_augmented_images(images, "output/synthetic_original", "original")
    save_augmented_images(augmented_images, "output/synthetic_augmented", "augmented")
    
    print("合成图片颜色增强完成！")
    return images, augmented_images


def batch_augmentation_example():
    """批量增强示例"""
    print("批量颜色增强示例...")
    
    # 创建多组测试数据
    batch_size = 5
    all_original = []
    all_augmented = []
    
    for batch_idx in range(batch_size):
        print(f"处理批次 {batch_idx + 1}/{batch_size}")
        
        # 为每个批次创建不同的测试图片
        images = []
        for i in range(3):
            img = np.random.randint(0, 255, (200, 200, 3), dtype=np.uint8)
            # 添加一些结构化内容
            cv2.putText(img, f"Batch{batch_idx+1}_Img{i+1}", (10, 50), 
                       cv2.FONT_HERSHEY_SIMPLEX, 0.7, (255, 255, 255), 2)
            images.append(img)
        
        # 创建增强器（每个批次使用不同的参数）
        augmenter = SynchronizedColorAugmentation(
            brightness=0.2 + batch_idx * 0.1,
            contrast=0.2 + batch_idx * 0.1,
            saturation=0.2 + batch_idx * 0.1,
            hue=0.05 + batch_idx * 0.02
        )
        
        # 执行增强
        augmented_images = augmenter.augment_images(images)
        
        # 保存结果
        save_augmented_images(images, f"output/batch_{batch_idx+1}/original", "original")
        save_augmented_images(augmented_images, f"output/batch_{batch_idx+1}/augmented", "augmented")
        
        all_original.extend(images)
        all_augmented.extend(augmented_images)
    
    print(f"批量处理完成！共处理 {len(all_original)} 张图片")


def compare_augmentation_effects():
    """比较不同增强参数的效果"""
    print("比较不同增强参数效果...")
    
    # 创建基础测试图片
    base_img = np.zeros((200, 200, 3), dtype=np.uint8)
    cv2.rectangle(base_img, (50, 50), (150, 150), (100, 150, 200), -1)
    cv2.circle(base_img, (100, 100), 30, (200, 100, 50), -1)
    
    images = [base_img.copy() for _ in range(3)]
    
    # 不同的增强配置
    configs = [
        {"name": "轻微增强", "brightness": 0.1, "contrast": 0.1, "saturation": 0.1, "hue": 0.05},
        {"name": "中等增强", "brightness": 0.3, "contrast": 0.3, "saturation": 0.3, "hue": 0.1},
        {"name": "强烈增强", "brightness": 0.5, "contrast": 0.5, "saturation": 0.5, "hue": 0.2},
        {"name": "极端增强", "brightness": 0.8, "contrast": 0.8, "saturation": 0.8, "hue": 0.3},
    ]
    
    for config in configs:
        print(f"测试配置: {config['name']}")
        
        augmenter = SynchronizedColorAugmentation(
            brightness=config["brightness"],
            contrast=config["contrast"],
            saturation=config["saturation"],
            hue=config["hue"],
            random_seed=42
        )
        
        augmented_images = augmenter.augment_images(images)
        
        # 保存结果
        output_dir = f"output/comparison/{config['name']}"
        save_augmented_images(augmented_images, output_dir, "augmented")
    
    # 保存原始图片用于对比
    save_augmented_images(images, "output/comparison/原始图片", "original")
    print("参数效果比较完成！")


if __name__ == "__main__":
    print("=== 颜色增强示例程序 ===\n")
    
    # 运行不同的示例
    print("1. 合成图片示例")
    example_with_synthetic_images()
    print("\n" + "="*50 + "\n")
    
    print("2. 批量增强示例")
    batch_augmentation_example()
    print("\n" + "="*50 + "\n")
    
    print("3. 参数效果比较")
    compare_augmentation_effects()
    print("\n" + "="*50 + "\n")
    
    print("所有示例运行完成！请查看 output/ 目录中的结果。")
