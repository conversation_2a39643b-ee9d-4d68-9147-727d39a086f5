#!/usr/bin/env python3
# -*-coding:utf-8-*-
"""
颜色增强模块
=============

功能说明：
1. 对列表中的3张cv2读取的图片进行颜色增强
2. 确保3张图片使用相同的随机颜色增强参数
3. 支持亮度、对比度、饱和度、色调的随机调整

作者：基于MMAction2 ColorJitter改进
"""

import cv2
import numpy as np
from typing import List, Tuple, Optional, Union
import random


class SynchronizedColorAugmentation:
    """
    同步颜色增强类
    
    对输入的3张图片应用相同的颜色增强参数，确保增强的一致性。
    
    Args:
        brightness (float | tuple[float]): 亮度调整范围，如果是float，范围为(1-brightness, 1+brightness)
        contrast (float | tuple[float]): 对比度调整范围，如果是float，范围为(1-contrast, 1+contrast)  
        saturation (float | tuple[float]): 饱和度调整范围，如果是float，范围为(1-saturation, 1+saturation)
        hue (float | tuple[float]): 色调调整范围，如果是float，范围为(-hue, hue)
        random_seed (int, optional): 随机种子，用于复现结果
    """
    
    def __init__(self, 
                 brightness: Union[float, Tuple[float, float]] = 0.3,
                 contrast: Union[float, Tuple[float, float]] = 0.3,
                 saturation: Union[float, Tuple[float, float]] = 0.3,
                 hue: Union[float, Tuple[float, float]] = 0.1,
                 random_seed: Optional[int] = None):
        
        self.brightness = self._check_input(brightness, 1, 1)
        self.contrast = self._check_input(contrast, 1, 1)
        self.saturation = self._check_input(saturation, 1, 1)
        self.hue = self._check_input(hue, 0.5, 0)
        
        if random_seed is not None:
            random.seed(random_seed)
            np.random.seed(random_seed)
        
        # 随机化增强操作的顺序
        self.fn_idx = np.random.permutation(4)
    
    @staticmethod
    def _check_input(val: Union[float, Tuple[float, float]], 
                     max_val: float, 
                     base: float) -> Tuple[float, float]:
        """检查并标准化输入参数"""
        if isinstance(val, tuple):
            assert base - max_val <= val[0] <= val[1] <= base + max_val, \
                f"参数范围错误: {val}, 应在 [{base - max_val}, {base + max_val}] 范围内"
            return val
        assert val <= max_val, f"参数值错误: {val}, 应 <= {max_val}"
        return (base - val, base + val)
    
    @staticmethod
    def _rgb_to_grayscale(img: np.ndarray) -> np.ndarray:
        """将RGB图像转换为灰度值"""
        return 0.2989 * img[..., 2] + 0.587 * img[..., 1] + 0.114 * img[..., 0]  # BGR格式
    
    @staticmethod
    def _adjust_brightness(img: np.ndarray, factor: float) -> np.ndarray:
        """调整亮度"""
        return img * factor
    
    @staticmethod
    def _adjust_contrast(img: np.ndarray, factor: float) -> np.ndarray:
        """调整对比度"""
        gray_mean = np.mean(SynchronizedColorAugmentation._rgb_to_grayscale(img))
        return factor * img + (1 - factor) * gray_mean
    
    @staticmethod
    def _adjust_saturation(img: np.ndarray, factor: float) -> np.ndarray:
        """调整饱和度"""
        gray = np.stack([SynchronizedColorAugmentation._rgb_to_grayscale(img)] * 3, axis=-1)
        return factor * img + (1 - factor) * gray
    
    @staticmethod
    def _adjust_hue(img: np.ndarray, factor: float) -> np.ndarray:
        """调整色调"""
        img_uint8 = np.clip(img, 0, 255).astype(np.uint8)
        hsv = cv2.cvtColor(img_uint8, cv2.COLOR_BGR2HSV)
        offset = int(factor * 179)  # OpenCV中H通道范围是0-179
        hsv[..., 0] = (hsv[..., 0].astype(np.int16) + offset) % 180
        img_bgr = cv2.cvtColor(hsv, cv2.COLOR_HSV2BGR)
        return img_bgr.astype(np.float32)
    
    def _generate_random_params(self) -> Tuple[float, float, float, float]:
        """生成随机增强参数"""
        brightness_factor = np.random.uniform(self.brightness[0], self.brightness[1])
        contrast_factor = np.random.uniform(self.contrast[0], self.contrast[1])
        saturation_factor = np.random.uniform(self.saturation[0], self.saturation[1])
        hue_factor = np.random.uniform(self.hue[0], self.hue[1])
        
        return brightness_factor, contrast_factor, saturation_factor, hue_factor
    
    def _apply_augmentation(self, 
                           img: np.ndarray, 
                           brightness_factor: float,
                           contrast_factor: float,
                           saturation_factor: float,
                           hue_factor: float) -> np.ndarray:
        """对单张图片应用颜色增强"""
        img = img.astype(np.float32)
        
        # 按随机顺序应用增强
        for fn_id in self.fn_idx:
            if fn_id == 0 and brightness_factor != 1:
                img = self._adjust_brightness(img, brightness_factor)
            elif fn_id == 1 and contrast_factor != 1:
                img = self._adjust_contrast(img, contrast_factor)
            elif fn_id == 2 and saturation_factor != 1:
                img = self._adjust_saturation(img, saturation_factor)
            elif fn_id == 3 and hue_factor != 0:
                img = self._adjust_hue(img, hue_factor)
        
        # 确保像素值在有效范围内
        img = np.clip(img, 0, 255).astype(np.uint8)
        return img
    
    def augment_images(self, images: List[np.ndarray]) -> List[np.ndarray]:
        """
        对图片列表进行同步颜色增强
        
        Args:
            images (List[np.ndarray]): cv2读取的图片列表，每张图片形状为(H, W, 3)，BGR格式
            
        Returns:
            List[np.ndarray]: 增强后的图片列表
            
        Raises:
            ValueError: 当输入图片数量不为3时
            AssertionError: 当图片格式不正确时
        """
        if len(images) != 3:
            raise ValueError(f"期望输入3张图片，实际输入{len(images)}张")
        
        # 验证图片格式
        for i, img in enumerate(images):
            assert isinstance(img, np.ndarray), f"第{i+1}张图片不是numpy数组"
            assert len(img.shape) == 3 and img.shape[2] == 3, \
                f"第{i+1}张图片形状错误: {img.shape}，期望(H, W, 3)"
        
        # 生成一组随机参数，用于所有图片
        brightness_factor, contrast_factor, saturation_factor, hue_factor = self._generate_random_params()
        
        print(f"颜色增强参数: 亮度={brightness_factor:.3f}, 对比度={contrast_factor:.3f}, "
              f"饱和度={saturation_factor:.3f}, 色调={hue_factor:.3f}")
        
        # 对所有图片应用相同的增强参数
        augmented_images = []
        for i, img in enumerate(images):
            augmented_img = self._apply_augmentation(
                img, brightness_factor, contrast_factor, saturation_factor, hue_factor
            )
            augmented_images.append(augmented_img)
            print(f"已处理第{i+1}张图片，形状: {augmented_img.shape}")
        
        return augmented_images


def demo_color_augmentation():
    """演示颜色增强功能"""
    import os
    
    # 创建测试图片（如果没有真实图片的话）
    test_images = []
    for i in range(3):
        # 创建不同颜色的测试图片
        img = np.zeros((224, 224, 3), dtype=np.uint8)
        if i == 0:  # 红色
            img[:, :, 2] = 200
        elif i == 1:  # 绿色
            img[:, :, 1] = 200
        else:  # 蓝色
            img[:, :, 0] = 200
        test_images.append(img)
    
    # 初始化颜色增强器
    augmenter = SynchronizedColorAugmentation(
        brightness=0.3,
        contrast=0.3,
        saturation=0.3,
        hue=0.1,
        random_seed=42  # 设置随机种子以便复现
    )
    
    # 执行颜色增强
    augmented_images = augmenter.augment_images(test_images)
    
    # 保存结果（可选）
    output_dir = "augmented_results"
    os.makedirs(output_dir, exist_ok=True)
    
    for i, (original, augmented) in enumerate(zip(test_images, augmented_images)):
        cv2.imwrite(f"{output_dir}/original_{i+1}.jpg", original)
        cv2.imwrite(f"{output_dir}/augmented_{i+1}.jpg", augmented)
    
    print(f"结果已保存到 {output_dir} 目录")
    return augmented_images


if __name__ == "__main__":
    # 运行演示
    demo_color_augmentation()

    # 读取3张图片
    images = [
        cv2.imread('image1.jpg'),
        cv2.imread('image2.jpg'),
        cv2.imread('image3.jpg')
    ]

    # 创建颜色增强器
    augmenter = SynchronizedColorAugmentation(
        brightness=0.3,  # 亮度变化范围 ±30%
        contrast=0.3,  # 对比度变化范围 ±30%
        saturation=0.3,  # 饱和度变化范围 ±30%
        hue=0.1  # 色调变化范围 ±0.1
    )

    # 执行同步颜色增强
    augmented_images = augmenter.augment_images(images)
