#!/usr/bin/env python3
# -*-coding:utf-8-*-
"""
颜色增强功能测试
================

测试SynchronizedColorAugmentation类的各项功能
"""

import cv2
import numpy as np
import unittest
from pathlib import Path
from color_augmentation import SynchronizedColorAugmentation


class TestSynchronizedColorAugmentation(unittest.TestCase):
    """颜色增强功能测试类"""
    
    def setUp(self):
        """测试前准备"""
        # 创建测试图片
        self.test_images = []
        for i in range(3):
            img = np.ones((100, 100, 3), dtype=np.uint8) * 128
            if i == 0:  # 红色区域
                img[20:80, 20:80, 2] = 255
            elif i == 1:  # 绿色区域
                img[20:80, 20:80, 1] = 255
            else:  # 蓝色区域
                img[20:80, 20:80, 0] = 255
            self.test_images.append(img)
    
    def test_initialization(self):
        """测试初始化"""
        # 测试默认参数
        augmenter = SynchronizedColorAugmentation()
        self.assertIsInstance(augmenter.brightness, tuple)
        self.assertIsInstance(augmenter.contrast, tuple)
        self.assertIsInstance(augmenter.saturation, tuple)
        self.assertIsInstance(augmenter.hue, tuple)
        
        # 测试自定义参数
        augmenter = SynchronizedColorAugmentation(
            brightness=0.5,
            contrast=(0.8, 1.2),
            saturation=0.3,
            hue=0.1
        )
        self.assertEqual(augmenter.brightness, (0.5, 1.5))
        self.assertEqual(augmenter.contrast, (0.8, 1.2))
        self.assertEqual(augmenter.saturation, (0.7, 1.3))
        self.assertEqual(augmenter.hue, (-0.1, 0.1))
    
    def test_input_validation(self):
        """测试输入验证"""
        augmenter = SynchronizedColorAugmentation()
        
        # 测试正确输入
        result = augmenter.augment_images(self.test_images)
        self.assertEqual(len(result), 3)
        
        # 测试错误的图片数量
        with self.assertRaises(ValueError):
            augmenter.augment_images(self.test_images[:2])
        
        with self.assertRaises(ValueError):
            augmenter.augment_images(self.test_images + [self.test_images[0]])
        
        # 测试错误的图片格式
        wrong_images = [np.ones((100, 100), dtype=np.uint8)] * 3  # 缺少颜色通道
        with self.assertRaises(AssertionError):
            augmenter.augment_images(wrong_images)
    
    def test_output_format(self):
        """测试输出格式"""
        augmenter = SynchronizedColorAugmentation(random_seed=42)
        result = augmenter.augment_images(self.test_images)
        
        # 检查输出数量
        self.assertEqual(len(result), 3)
        
        # 检查输出格式
        for i, img in enumerate(result):
            self.assertIsInstance(img, np.ndarray)
            self.assertEqual(img.shape, self.test_images[i].shape)
            self.assertEqual(img.dtype, np.uint8)
            self.assertTrue(np.all(img >= 0))
            self.assertTrue(np.all(img <= 255))
    
    def test_consistency(self):
        """测试增强一致性"""
        augmenter = SynchronizedColorAugmentation(random_seed=42)
        
        # 多次运行，检查结果是否一致
        result1 = augmenter.augment_images(self.test_images)
        
        # 重新设置相同的随机种子
        augmenter = SynchronizedColorAugmentation(random_seed=42)
        result2 = augmenter.augment_images(self.test_images)
        
        # 结果应该相同
        for img1, img2 in zip(result1, result2):
            np.testing.assert_array_equal(img1, img2)
    
    def test_parameter_effects(self):
        """测试参数效果"""
        # 测试无增强（所有参数为默认值）
        augmenter = SynchronizedColorAugmentation(
            brightness=0, contrast=0, saturation=0, hue=0
        )
        result = augmenter.augment_images(self.test_images)
        
        # 结果应该与原图相似（可能有微小的数值误差）
        for original, augmented in zip(self.test_images, result):
            diff = np.abs(original.astype(np.float32) - augmented.astype(np.float32))
            self.assertTrue(np.mean(diff) < 5)  # 平均差异小于5
    
    def test_extreme_parameters(self):
        """测试极端参数"""
        # 测试极端亮度
        augmenter = SynchronizedColorAugmentation(
            brightness=0.9, contrast=0, saturation=0, hue=0, random_seed=42
        )
        result = augmenter.augment_images(self.test_images)
        
        # 结果应该仍然有效
        for img in result:
            self.assertTrue(np.all(img >= 0))
            self.assertTrue(np.all(img <= 255))
    
    def test_different_image_sizes(self):
        """测试不同尺寸的图片"""
        # 创建不同尺寸的测试图片
        sizes = [(50, 50), (100, 200), (300, 150)]
        different_size_images = []
        
        for size in sizes:
            img = np.random.randint(0, 255, (*size, 3), dtype=np.uint8)
            different_size_images.append(img)
        
        augmenter = SynchronizedColorAugmentation(random_seed=42)
        result = augmenter.augment_images(different_size_images)
        
        # 检查输出尺寸是否保持不变
        for original, augmented in zip(different_size_images, result):
            self.assertEqual(original.shape, augmented.shape)


def run_visual_test():
    """运行可视化测试"""
    print("运行可视化测试...")
    
    # 创建测试图片
    test_images = []
    
    # 图片1: 纯色块
    img1 = np.zeros((200, 200, 3), dtype=np.uint8)
    img1[:100, :, 2] = 255  # 上半部分红色
    img1[100:, :, 1] = 255  # 下半部分绿色
    test_images.append(img1)
    
    # 图片2: 渐变
    img2 = np.zeros((200, 200, 3), dtype=np.uint8)
    for i in range(200):
        img2[i, :, 0] = int(255 * i / 200)  # B通道渐变
        img2[:, i, 1] = int(255 * i / 200)  # G通道渐变
    test_images.append(img2)
    
    # 图片3: 几何图形
    img3 = np.ones((200, 200, 3), dtype=np.uint8) * 100
    cv2.circle(img3, (100, 100), 50, (255, 100, 50), -1)
    cv2.rectangle(img3, (50, 50), (150, 80), (50, 255, 100), -1)
    test_images.append(img3)
    
    # 创建输出目录
    output_dir = Path("test_output")
    output_dir.mkdir(exist_ok=True)
    
    # 保存原始图片
    for i, img in enumerate(test_images):
        cv2.imwrite(str(output_dir / f"original_{i+1}.jpg"), img)
    
    # 测试不同的增强强度
    intensities = [
        {"name": "轻微", "params": {"brightness": 0.1, "contrast": 0.1, "saturation": 0.1, "hue": 0.05}},
        {"name": "中等", "params": {"brightness": 0.3, "contrast": 0.3, "saturation": 0.3, "hue": 0.1}},
        {"name": "强烈", "params": {"brightness": 0.5, "contrast": 0.5, "saturation": 0.5, "hue": 0.2}},
    ]
    
    for intensity in intensities:
        print(f"测试{intensity['name']}增强...")
        
        augmenter = SynchronizedColorAugmentation(
            random_seed=42,
            **intensity['params']
        )
        
        augmented_images = augmenter.augment_images(test_images)
        
        # 保存增强后的图片
        for i, img in enumerate(augmented_images):
            filename = f"{intensity['name']}_augmented_{i+1}.jpg"
            cv2.imwrite(str(output_dir / filename), img)
    
    print(f"可视化测试完成！结果保存在 {output_dir} 目录")


def benchmark_performance():
    """性能基准测试"""
    import time
    
    print("运行性能基准测试...")
    
    # 创建大尺寸测试图片
    large_images = []
    for i in range(3):
        img = np.random.randint(0, 255, (1080, 1920, 3), dtype=np.uint8)
        large_images.append(img)
    
    augmenter = SynchronizedColorAugmentation()
    
    # 预热
    _ = augmenter.augment_images([img[:100, :100] for img in large_images])
    
    # 性能测试
    num_runs = 10
    start_time = time.time()
    
    for _ in range(num_runs):
        _ = augmenter.augment_images(large_images)
    
    end_time = time.time()
    avg_time = (end_time - start_time) / num_runs
    
    print(f"平均处理时间: {avg_time:.3f}秒")
    print(f"处理速度: {3/avg_time:.1f} 张图片/秒")
    print(f"像素处理速度: {3*1080*1920/avg_time/1e6:.1f} 百万像素/秒")


if __name__ == "__main__":
    print("=== 颜色增强功能测试 ===\n")
    
    # 运行单元测试
    print("1. 运行单元测试...")
    unittest.main(argv=[''], exit=False, verbosity=2)
    print("\n" + "="*50 + "\n")
    
    # 运行可视化测试
    print("2. 运行可视化测试...")
    run_visual_test()
    print("\n" + "="*50 + "\n")
    
    # 运行性能测试
    print("3. 运行性能测试...")
    benchmark_performance()
    print("\n" + "="*50 + "\n")
    
    print("所有测试完成！")
