# 同步颜色增强工具

## 功能简介

这是一个专门为处理3张cv2读取图片的颜色增强工具，确保3张图片使用**完全相同的随机颜色增强参数**，保持增强的一致性。

## 主要特性

✅ **同步增强**: 3张图片使用相同的随机颜色增强参数
✅ **支持cv2格式**: 直接处理cv2.imread()读取的BGR格式图片
✅ **多种颜色调整**: 支持亮度、对比度、饱和度、色调调整
✅ **概率控制**: 支持设置执行增强的概率（如30%概率执行）
✅ **参数验证**: 完整的输入验证和错误处理
✅ **可复现结果**: 支持设置随机种子
✅ **灵活配置**: 可自定义各种增强参数范围

## 文件说明

- `color_augmentation.py` - 核心颜色增强类
- `color_augmentation_example.py` - 详细使用示例
- `test_color_augmentation.py` - 完整测试套件
- `README_颜色增强.md` - 本说明文档

## 快速开始

### 基本使用

```python
import cv2
from color_augmentation import SynchronizedColorAugmentation

# 读取3张图片
images = [
    cv2.imread('image1.jpg'),
    cv2.imread('image2.jpg'), 
    cv2.imread('image3.jpg')
]

# 创建颜色增强器
augmenter = SynchronizedColorAugmentation(
    brightness=0.3,    # 亮度变化范围 ±30%
    contrast=0.3,      # 对比度变化范围 ±30%
    saturation=0.3,    # 饱和度变化范围 ±30%
    hue=0.1,          # 色调变化范围 ±0.1
    prob=0.3          # 30%的概率执行增强
)

# 执行同步颜色增强
augmented_images = augmenter.augment_images(images)

# 保存结果
for i, img in enumerate(augmented_images):
    cv2.imwrite(f'augmented_{i+1}.jpg', img)
```

### 参数说明

| 参数 | 类型 | 默认值 | 说明 |
|------|------|--------|------|
| `brightness` | float/tuple | 0.3 | 亮度调整范围，0.3表示(0.7, 1.3) |
| `contrast` | float/tuple | 0.3 | 对比度调整范围，0.3表示(0.7, 1.3) |
| `saturation` | float/tuple | 0.3 | 饱和度调整范围，0.3表示(0.7, 1.3) |
| `hue` | float/tuple | 0.1 | 色调调整范围，0.1表示(-0.1, 0.1) |
| `prob` | float | 1.0 | 执行增强的概率，范围[0.0, 1.0] |
| `random_seed` | int/None | None | 随机种子，设置后可复现结果 |

### 高级用法

```python
# 使用自定义参数范围
augmenter = SynchronizedColorAugmentation(
    brightness=(0.8, 1.2),  # 直接指定范围
    contrast=0.4,           # 使用默认计算: (0.6, 1.4)
    saturation=0.2,         # 较小的变化: (0.8, 1.2)
    hue=0.15,              # 较大的色调变化: (-0.15, 0.15)
    prob=0.5,              # 50%概率执行增强
    random_seed=42         # 固定随机种子
)

# 批量处理多组图片，展示概率效果
image_groups = [
    [img1_1, img1_2, img1_3],
    [img2_1, img2_2, img2_3],
    # ... 更多组
]

augmented_count = 0
for i, images in enumerate(image_groups):
    print(f"处理第{i+1}组图片:")
    augmented = augmenter.augment_images(images)
    # 统计实际执行增强的次数
    # 保存或进一步处理
```

### 概率控制示例

```python
# 30%概率执行增强，适用于数据增强场景
train_augmenter = SynchronizedColorAugmentation(
    brightness=0.3,
    contrast=0.3,
    saturation=0.3,
    hue=0.1,
    prob=0.3  # 30%概率
)

# 处理训练数据
for batch_images in training_data:
    # 每个batch有30%概率被增强
    augmented_batch = train_augmenter.augment_images(batch_images)
    # 继续训练流程...

# 测试时不使用增强
test_augmenter = SynchronizedColorAugmentation(prob=0.0)  # 0%概率，不增强
```

## 运行示例

### 1. 基本功能测试
```bash
python -c "
from color_augmentation import SynchronizedColorAugmentation
import numpy as np

# 创建测试图片
images = [np.ones((100, 100, 3), dtype=np.uint8) * (50 + i*50) for i in range(3)]

# 执行增强
augmenter = SynchronizedColorAugmentation(random_seed=42)
result = augmenter.augment_images(images)
print('测试成功！')
"
```

### 2. 运行完整示例
```bash
python color_augmentation_example.py
```

### 3. 运行测试套件
```bash
python test_color_augmentation.py
```

## 输出示例

运行时会显示增强参数：
```
颜色增强参数: 亮度=1.139, 对比度=1.059, 饱和度=0.794, 色调=-0.069
已处理第1张图片，形状: (224, 224, 3)
已处理第2张图片，形状: (224, 224, 3)
已处理第3张图片，形状: (224, 224, 3)
```

## 注意事项

1. **输入要求**: 必须输入恰好3张图片
2. **图片格式**: 支持cv2读取的BGR格式图片 (H, W, 3)
3. **数据类型**: 输入和输出都是uint8格式
4. **内存使用**: 处理大图片时注意内存占用
5. **参数范围**: 建议参数不要设置过大，避免图片失真

## 错误处理

工具包含完整的错误处理：

- 图片数量检查（必须为3张）
- 图片格式验证（必须为3通道）
- 参数范围验证
- 数据类型检查

## 性能优化

- 使用numpy向量化操作
- 避免不必要的数据复制
- 支持不同尺寸图片的批量处理

## 扩展功能

如需处理不同数量的图片或添加其他增强功能，可以基于现有代码进行扩展。核心思想是确保所有图片使用相同的随机参数。

---

**作者**: 基于MMAction2 ColorJitter改进  
**版本**: 1.0  
**更新时间**: 2024年
